import { Suspense, lazy } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";

// Dynamic imports for both ideas
const AudioShowcase = lazy(() => import("@/components/landing/AudioShowcase"));
const FeaturesSectionWithAudio = lazy(() => import("@/components/landing/FeaturesSectionWithAudio"));

// Loading component
const SectionLoader = () => (
  <div className="flex items-center justify-center py-20">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400"></div>
  </div>
);

const LandingComparison = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col">
      {/* Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-gray-900 to-black -z-10"></div>

      {/* Header */}
      <div className="relative z-30 w-full py-6 px-4 md:px-8">
        <div className="container mx-auto flex justify-between items-center">
          <Button
            variant="ghost"
            onClick={() => navigate("/")}
            className="text-cyan-300 hover:text-cyan-200 hover:bg-cyan-500/10 border border-cyan-400/30 rounded-full"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Landing
          </Button>
          <h1 className="text-2xl font-semibold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
            Landing Page Ideas Comparison
          </h1>
        </div>
      </div>

      {/* Idea 1 - Dedicated Audio Showcase Section */}
      <section className="relative py-16">
        <div className="container mx-auto px-4 md:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-semibold mb-4 text-white">
              Idea 1: <span className="bg-gradient-to-r from-emerald-400 to-teal-400 bg-clip-text text-transparent">Dedicated Audio Showcase</span>
            </h2>
            <p className="text-white/70 text-lg max-w-3xl mx-auto">
              A separate AudioShowcase section similar to VideoShowcase, placed after the video section. 
              Features dedicated audio cards with waveform visualizations, play buttons, and clear Pro/Coming Soon indicators.
            </p>
          </div>

          <Suspense fallback={<SectionLoader />}>
            <AudioShowcase />
          </Suspense>
        </div>
      </section>

      {/* Divider */}
      <div className="relative py-8">
        <div className="container mx-auto px-4 md:px-8">
          <div className="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
        </div>
      </div>

      {/* Idea 2 - Integrated Audio in Features */}
      <section className="relative py-16">
        <div className="container mx-auto px-4 md:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-semibold mb-4 text-white">
              Idea 2: <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Integrated Audio Features</span>
            </h2>
            <p className="text-white/70 text-lg max-w-3xl mx-auto">
              Audio generation integrated into the existing FeaturesSection as a third column alongside images and video. 
              Creates a unified 3-column layout showing all AI capabilities in one cohesive section.
            </p>
          </div>

          <Suspense fallback={<SectionLoader />}>
            <FeaturesSectionWithAudio />
          </Suspense>
        </div>
      </section>

      {/* Comparison Summary */}
      <section className="relative py-16">
        <div className="container mx-auto px-4 md:px-8">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-2xl font-semibold mb-8 text-center text-white">
              Comparison Summary
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Idea 1 Pros/Cons */}
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
                <h4 className="text-xl font-medium mb-4 text-emerald-300">Idea 1: Dedicated Section</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-green-400 font-medium mb-2">Pros:</h5>
                    <ul className="text-white/70 text-sm space-y-1">
                      <li>• More space for audio showcase</li>
                      <li>• Better audio visualization</li>
                      <li>• Clear separation of features</li>
                      <li>• Follows existing pattern</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="text-red-400 font-medium mb-2">Cons:</h5>
                    <ul className="text-white/70 text-sm space-y-1">
                      <li>• Longer page scroll</li>
                      <li>• More sections to maintain</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Idea 2 Pros/Cons */}
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
                <h4 className="text-xl font-medium mb-4 text-purple-300">Idea 2: Integrated Features</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-green-400 font-medium mb-2">Pros:</h5>
                    <ul className="text-white/70 text-sm space-y-1">
                      <li>• Compact, unified layout</li>
                      <li>• Shows all capabilities together</li>
                      <li>• Shorter page length</li>
                      <li>• Easier to compare features</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="text-red-400 font-medium mb-2">Cons:</h5>
                    <ul className="text-white/70 text-sm space-y-1">
                      <li>• Less space per feature</li>
                      <li>• Smaller audio visualization</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default LandingComparison;
