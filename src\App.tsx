
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { useEffect, useRef } from "react";
import Index from "./pages/Index";
import LandingComparison from "./pages/LandingComparison";
import NotFound from "./pages/NotFound";
import SignUp from "./pages/SignUp";
import SignIn from "./pages/SignIn";
import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import UserProfile from "./pages/UserProfile";
import Dashboard from "./pages/Dashboard";
import ImageGenerator from "./pages/ImageGenerator";
import AdvancedImageTools from "./pages/AdvancedImageTools";
import BackgroundRemovalPage from "./pages/BackgroundRemovalPage";
import ColorGuidedGenerationPage from "./pages/ColorGuidedGenerationPage";
import ImageVariationPage from "./pages/ImageVariationPage";
import ImageConditioningPage from "./pages/ImageConditioningPage";
import VideoGenerator from "./pages/VideoGenerator";
import VoiceGenerator from "./pages/VoiceGenerator";
import MultiShotVideoGenerator from "./pages/MultiShotVideoGenerator";
import Superadmin from "./pages/Superadmin";
import ProtectedRoute from "./components/ProtectedRoute";
import SuperadminRoute from "./components/SuperadminRoute";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Prevent aggressive refetching that causes stuck states
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      refetchOnReconnect: false,
      
      // Set reasonable stale and cache times
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      
      // Limit retries to prevent infinite loops
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      
      // Add network timeout
      networkMode: 'online',
    },
    mutations: {
      // Limit mutation retries
      retry: 2,
      retryDelay: 1000,
      networkMode: 'online',
    },
  },
});

// Add global error handler for debugging
queryClient.setMutationDefaults(['*'], {
  onError: (error: any) => {
    if (import.meta.env.DEV) {
      console.error('React Query Mutation Error:', error);
    }
  },
});

queryClient.setQueryDefaults(['*'], {
  onError: (error: any) => {
    if (import.meta.env.DEV) {
      console.error('React Query Query Error:', error);
    }
  },
});

// Component to handle route changes and stop audio
const RouteChangeHandler = () => {
  const location = useLocation();
  const prevPathRef = useRef<string>('');

  useEffect(() => {
    const currentPath = location.pathname;
    const prevPath = prevPathRef.current;
    
    // Stop all audio when navigating away from voice-generator page
    if (prevPath === '/voice-generator' && currentPath !== '/voice-generator') {
      const audioElements = document.querySelectorAll('audio');
      audioElements.forEach(audio => {
        if (!audio.paused) {
          audio.pause();
          audio.currentTime = 0;
        }
      });
    }
    
    prevPathRef.current = currentPath;
  }, [location.pathname]);

  return null;
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <RouteChangeHandler />
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/landing-comparison" element={<LandingComparison />} />
            <Route path="/signup" element={<SignUp />} />
            <Route path="/signin" element={<SignIn />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
            <Route path="/profile" element={<ProtectedRoute><UserProfile /></ProtectedRoute>} />
            <Route path="/image-generator" element={<ProtectedRoute><ImageGenerator /></ProtectedRoute>} />
            <Route path="/advanced-image-tools" element={<ProtectedRoute><AdvancedImageTools /></ProtectedRoute>} />
            <Route path="/background-removal" element={<ProtectedRoute><BackgroundRemovalPage /></ProtectedRoute>} />
            <Route path="/color-guided-generation" element={<ProtectedRoute><ColorGuidedGenerationPage /></ProtectedRoute>} />
            <Route path="/image-variation" element={<ProtectedRoute><ImageVariationPage /></ProtectedRoute>} />
            <Route path="/image-conditioning" element={<ProtectedRoute><ImageConditioningPage /></ProtectedRoute>} />
            <Route path="/video-generator" element={<ProtectedRoute><VideoGenerator /></ProtectedRoute>} />
            <Route path="/multi-shot-video-generator" element={<ProtectedRoute><MultiShotVideoGenerator /></ProtectedRoute>} />
            <Route path="/voice-generator" element={<ProtectedRoute><VoiceGenerator /></ProtectedRoute>} />
            <Route path="/superadmin" element={<SuperadminRoute><Superadmin /></SuperadminRoute>} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
