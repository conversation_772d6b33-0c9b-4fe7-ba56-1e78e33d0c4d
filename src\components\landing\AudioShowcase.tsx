import { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Mic, Music, Play, Pause, Volume2, Sparkles } from "lucide-react";

// Audio model data constants
const AUDIO_MODELS = [
  {
    id: 'basic',
    name: 'Basic Voice',
    badgeClass: 'px-4 py-2 bg-black/60 backdrop-blur-sm text-white rounded-full text-sm font-medium',
    containerClass: 'relative overflow-hidden rounded-2xl shadow-xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-gray-700/30',
    isComingSoon: false
  },
  {
    id: 'pro',
    name: 'Pro Voice',
    badgeClass: 'px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-500 text-white rounded-full text-sm font-medium',
    containerClass: 'relative overflow-hidden rounded-2xl shadow-xl bg-gradient-to-br from-purple-900/30 to-pink-900/30 backdrop-blur-sm border border-purple-500/30',
    isComingSoon: false
  },
  {
    id: 'music',
    name: 'Music Generator',
    badgeClass: 'px-4 py-2 bg-gradient-to-r from-emerald-600 to-teal-500 text-white rounded-full text-sm font-medium',
    containerClass: 'relative overflow-hidden rounded-2xl shadow-xl bg-gradient-to-br from-emerald-900/30 to-teal-900/30 backdrop-blur-sm border border-emerald-500/30',
    isComingSoon: true
  }
] as const;

// Sample audio data with placeholder MP3 links
const SHOWCASE_AUDIO = [
  {
    id: 'voice-basic',
    title: "Welcome to VibeNecto",
    description: "Natural voice generation for marketing content",
    model: 'basic',
    duration: "0:08",
    waveform: "M2 12h20M6 8h12M4 16h16",
    audioUrl: "/audio/voice-basic-sample.mp3" // Placeholder - replace with actual MP3
  },
  {
    id: 'voice-pro',
    title: "Premium Brand Narration",
    description: "Professional voice with enhanced emotion and clarity",
    model: 'pro',
    duration: "0:12",
    waveform: "M2 12h20M3 8h18M5 16h14M7 4h10M9 20h6",
    audioUrl: "/audio/voice-pro-sample.mp3" // Placeholder - replace with actual MP3
  },
  {
    id: 'music-sample',
    title: "Upbeat Marketing Track",
    description: "AI-generated background music for video content",
    model: 'music',
    duration: "0:30",
    waveform: "M2 12h20M1 8h22M3 16h18M2 4h20M4 20h16",
    audioUrl: "/audio/music-sample.mp3" // Placeholder - replace with actual MP3
  }
] as const;

// Audio card component
interface AudioCardProps {
  id: string;
  title: string;
  description: string;
  modelId: 'basic' | 'pro' | 'music';
  duration: string;
  waveform: string;
  audioUrl: string;
}

const AudioCard = ({ id, title, description, modelId, duration, waveform, audioUrl }: AudioCardProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [audio, setAudio] = useState<HTMLAudioElement | null>(null);
  const model = AUDIO_MODELS.find(m => m.id === modelId);
  if (!model) return null;

  const handlePlayPause = () => {
    if (model.isComingSoon) return;

    if (isPlaying && audio) {
      audio.pause();
      audio.currentTime = 0;
      setIsPlaying(false);
    } else {
      // Create new audio element
      const newAudio = new Audio(audioUrl);
      newAudio.addEventListener('ended', () => {
        setIsPlaying(false);
        setAudio(null);
      });
      newAudio.addEventListener('error', () => {
        console.log(`Audio file not found: ${audioUrl}`);
        setIsPlaying(false);
        setAudio(null);
      });

      newAudio.play().catch(() => {
        console.log(`Could not play audio: ${audioUrl}`);
        setIsPlaying(false);
      });

      setAudio(newAudio);
      setIsPlaying(true);
    }
  };

  return (
    <div className="group">
      <div className="relative overflow-hidden rounded-2xl border border-white/10 backdrop-blur-sm">
        {/* Audio Visualization */}
        <div className="p-8 h-48 flex flex-col justify-center items-center">
          {/* Waveform Visualization */}
          <div className="w-full h-16 mb-6 flex items-center justify-center">
            <svg
              className="w-full h-full opacity-60"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              {waveform.split('M').slice(1).map((path, index) => (
                <path
                  key={index}
                  d={`M${path}`}
                  strokeWidth="2"
                  strokeLinecap="round"
                  className={isPlaying ? "animate-pulse" : ""}
                />
              ))}
            </svg>
          </div>

          {/* Play Button */}
          <Button
            onClick={handlePlayPause}
            size="lg"
            className="w-16 h-16 rounded-full bg-white/10 hover:bg-white/20 border border-white/20 backdrop-blur-sm transition-all duration-300"
            disabled={model.isComingSoon}
          >
            {isPlaying ? (
              <Pause className="w-6 h-6 text-white" />
            ) : (
              <Play className="w-6 h-6 text-white ml-1" />
            )}
          </Button>

          {/* Duration */}
          <p className="text-white/60 text-sm mt-4">{duration}</p>
        </div>

        {/* Model Badge */}
        <div className="absolute top-6 left-6 z-10 flex gap-3">
          <span className={model.badgeClass}>
            {model.name}
          </span>
          {model.isComingSoon && (
            <span className="px-3 py-1 bg-amber-500/20 backdrop-blur-sm text-amber-300 rounded-full text-sm font-medium">
              Coming Soon
            </span>
          )}
        </div>

        {/* Content Info */}
        <div className="absolute bottom-6 left-6 right-6">
          <h4 className="text-white font-medium text-lg mb-2">{title}</h4>
          <p className="text-white/70 text-sm">{description}</p>
        </div>
      </div>
    </div>
  );
};

const AudioShowcase = () => {
  const navigate = useNavigate();

  return (
    <div className="mt-24 w-full" data-section="audio-generation">
      {/* Feature header */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500/20 to-teal-500/20 flex items-center justify-center">
            <Volume2 className="w-5 h-5 text-emerald-400" />
          </div>
          <h3 className="text-2xl md:text-3xl font-medium bg-gradient-to-r from-emerald-300 to-teal-300 bg-clip-text text-transparent">Audio Generation</h3>
        </div>
        <p className="text-white/80 text-lg max-w-2xl mx-auto">
          AI-powered voice and music creation for your content
        </p>
      </div>

      {/* Audio Showcase Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
        {SHOWCASE_AUDIO.map((audio) => (
          <AudioCard
            key={audio.id}
            id={audio.id}
            title={audio.title}
            description={audio.description}
            modelId={audio.model}
            duration={audio.duration}
            waveform={audio.waveform}
            audioUrl={audio.audioUrl}
          />
        ))}
      </div>

      {/* Pro Voice Features */}
      <div className="mt-12 text-center">
        <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600/20 to-pink-500/20 backdrop-blur-md text-purple-300 rounded-full text-sm font-medium border border-purple-500/30">
          <Sparkles className="w-4 h-4" />
          <span>Pro Voice: Enhanced emotion, clarity, and natural speech patterns</span>
        </div>
      </div>
    </div>
  );
};

export default AudioShowcase;
