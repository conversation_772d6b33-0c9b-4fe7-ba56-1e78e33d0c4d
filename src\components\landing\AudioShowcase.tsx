import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Mic, Music, Play, Pause, Volume2, Sparkles } from "lucide-react";

// Audio model data constants
const AUDIO_MODELS = [
  {
    id: 'basic',
    name: 'Basic Voice',
    badgeClass: 'px-4 py-2 bg-black/60 backdrop-blur-sm text-white rounded-full text-sm font-medium',
    containerClass: 'relative overflow-hidden rounded-2xl shadow-xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-gray-700/30',
    isComingSoon: false
  },
  {
    id: 'pro',
    name: 'Pro Voice',
    badgeClass: 'px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-500 text-white rounded-full text-sm font-medium',
    containerClass: 'relative overflow-hidden rounded-2xl shadow-xl bg-gradient-to-br from-purple-900/30 to-pink-900/30 backdrop-blur-sm border border-purple-500/30',
    isComingSoon: false
  },
  {
    id: 'music',
    name: 'Music Generator',
    badgeClass: 'px-4 py-2 bg-gradient-to-r from-emerald-600 to-teal-500 text-white rounded-full text-sm font-medium',
    containerClass: 'relative overflow-hidden rounded-2xl shadow-xl bg-gradient-to-br from-emerald-900/30 to-teal-900/30 backdrop-blur-sm border border-emerald-500/30',
    isComingSoon: true
  }
] as const;

// Sample audio data with placeholder MP3 links
const SHOWCASE_AUDIO = [
  {
    id: 'voice-basic',
    title: "Welcome to VibeNecto",
    description: "Natural voice generation for marketing content",
    model: 'basic',
    duration: "0:08",
    waveform: "M2 12h20M6 8h12M4 16h16",
    audioUrl: "src\Audio\basic-model.mp3" // File should be in public/Audio/ folder
  },
  {
    id: 'voice-pro',
    title: "Premium Brand Narration",
    description: "Professional voice with enhanced emotion and clarity",
    model: 'pro',
    duration: "0:12",
    waveform: "M2 12h20M3 8h18M5 16h14M7 4h10M9 20h6",
    audioUrl: "/audio/voice-pro-sample.mp3" // Placeholder - replace with actual MP3
  },
  {
    id: 'music-sample',
    title: "Upbeat Marketing Track",
    description: "AI-generated background music for video content",
    model: 'music',
    duration: "0:30",
    waveform: "M2 12h20M1 8h22M3 16h18M2 4h20M4 20h16",
    audioUrl: "/audio/music-sample.mp3" // Placeholder - replace with actual MP3
  }
] as const;

// Audio card component
interface AudioCardProps {
  id: string;
  title: string;
  description: string;
  modelId: 'basic' | 'pro' | 'music';
  duration: string;
  waveform: string;
  audioUrl: string;
}

const AudioCard = ({ id, title, description, modelId, duration, waveform, audioUrl }: AudioCardProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const model = AUDIO_MODELS.find(m => m.id === modelId);
  if (!model) return null;

  // Cleanup audio on unmount
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  const handlePlayPause = () => {
    if (model.isComingSoon) return;

    console.log(`Attempting to play audio: ${audioUrl}`);
    console.log(`Full URL will be: ${window.location.origin}${audioUrl}`);

    if (isPlaying && audioRef.current) {
      // Pause current audio
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
    } else {
      // Stop any existing audio first
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }

      // Test if file exists first
      fetch(audioUrl, { method: 'HEAD' })
        .then(response => {
          console.log(`File check response: ${response.status} ${response.statusText}`);
          if (!response.ok) {
            throw new Error(`File not found: ${response.status} ${response.statusText}`);
          }

          // Create new audio element
          const newAudio = new Audio(audioUrl);
          audioRef.current = newAudio;

          // Set up event listeners
          newAudio.addEventListener('ended', () => {
            console.log('Audio ended');
            setIsPlaying(false);
            audioRef.current = null;
          });

          newAudio.addEventListener('error', (e) => {
            console.error(`Audio error:`, e);
            const audioElement = e.target as HTMLAudioElement;
            console.error(`Error details:`, audioElement?.error);
            alert(`Audio error: ${audioElement?.error?.message || 'Unknown error'}`);
            setIsPlaying(false);
            audioRef.current = null;
          });

          newAudio.addEventListener('loadstart', () => {
            console.log(`Audio loading started: ${audioUrl}`);
          });

          newAudio.addEventListener('canplay', () => {
            console.log(`Audio can play: ${audioUrl}`);
          });

          newAudio.addEventListener('loadeddata', () => {
            console.log(`Audio data loaded: ${audioUrl}`);
          });

          // Try to play
          newAudio.play()
            .then(() => {
              console.log(`Audio playing successfully: ${audioUrl}`);
              setIsPlaying(true);
            })
            .catch((error) => {
              console.error(`Play failed:`, error);
              alert(`Play failed: ${error.message}`);
              setIsPlaying(false);
              audioRef.current = null;
            });
        })
        .catch(error => {
          console.error(`File check failed:`, error);
          alert(`File not accessible: ${audioUrl}\nError: ${error.message}\n\nMake sure the file is in: public/Audio/basic-model.mp3`);
        });
    }
  };

  return (
    <div className="group">
      <div className="relative overflow-hidden rounded-2xl border border-white/10 h-80">
        {/* Model Badge */}
        <div className="absolute top-4 left-4 z-10 flex gap-2">
          <span className={model.badgeClass}>
            {model.name}
          </span>
          {model.isComingSoon && (
            <span className="px-3 py-1 bg-amber-500/20 backdrop-blur-sm text-amber-300 rounded-full text-sm font-medium">
              Coming Soon
            </span>
          )}
        </div>

        {/* Main Content Area */}
        <div className="p-6 h-full flex flex-col">
          {/* Title and Description at top */}
          <div className="mt-8 mb-6">
            <h4 className="text-white font-medium text-lg mb-2">{title}</h4>
            <p className="text-white/70 text-sm">{description}</p>
          </div>

          {/* Center area with waveform and play button */}
          <div className="flex-1 flex flex-col justify-center items-center">
            {/* Waveform Visualization */}
            <div className="w-full h-12 mb-6 flex items-center justify-center">
              <div className="flex items-center justify-center gap-1 w-3/4">
                {[...Array(12)].map((_, index) => (
                  <div
                    key={index}
                    className={`bg-white/40 rounded-full transition-all duration-300 ${
                      isPlaying ? 'animate-pulse' : ''
                    }`}
                    style={{
                      width: '3px',
                      height: `${Math.random() * 24 + 8}px`,
                      animationDelay: `${index * 0.1}s`
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Play Button */}
            <Button
              onClick={handlePlayPause}
              size="lg"
              className="w-14 h-14 rounded-full bg-white/20 hover:bg-white/30 border-2 border-white/30 transition-all duration-300 shadow-lg"
              disabled={model.isComingSoon}
            >
              {isPlaying ? (
                <Pause className="w-5 h-5 text-white" />
              ) : (
                <Play className="w-5 h-5 text-white ml-0.5" />
              )}
            </Button>
          </div>

          {/* Duration at bottom */}
          <div className="text-center">
            <p className="text-white/60 text-sm">{duration}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const AudioShowcase = () => {

  return (
    <div className="mt-24 w-full" data-section="audio-generation">
      {/* Feature header */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500/20 to-teal-500/20 flex items-center justify-center">
            <Volume2 className="w-5 h-5 text-emerald-400" />
          </div>
          <h3 className="text-2xl md:text-3xl font-medium bg-gradient-to-r from-emerald-300 to-teal-300 bg-clip-text text-transparent">Audio Generation</h3>
        </div>
        <p className="text-white/80 text-lg max-w-2xl mx-auto">
          AI-powered voice and music creation for your content
        </p>
      </div>

      {/* Audio Showcase Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
        {SHOWCASE_AUDIO.map((audio) => (
          <AudioCard
            key={audio.id}
            id={audio.id}
            title={audio.title}
            description={audio.description}
            modelId={audio.model}
            duration={audio.duration}
            waveform={audio.waveform}
            audioUrl={audio.audioUrl}
          />
        ))}
      </div>

      {/* Pro Voice Features */}
      <div className="mt-12 text-center">
        <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600/20 to-pink-500/20 backdrop-blur-md text-purple-300 rounded-full text-sm font-medium border border-purple-500/30">
          <Sparkles className="w-4 h-4" />
          <span>Pro Voice: Enhanced emotion, clarity, and natural speech patterns</span>
        </div>
      </div>
    </div>
  );
};

export default AudioShowcase;
