import { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Sparkles, Wand2, Palette, Image as ImageIcon, Video, Volume2, Mic, Music, Play, Pause } from "lucide-react";

// Feature data constants
const FEATURE_MODELS = [
  {
    id: 'basic',
    name: 'Basic Model',
    badgeClass: 'px-4 py-2 bg-black/60 backdrop-blur-sm text-white rounded-full text-sm font-medium',
    isComingSoon: false
  },
  {
    id: 'pro',
    name: 'Pro Model',
    badgeClass: 'px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-500 text-white rounded-full text-sm font-medium',
    styleControlBadgeClass: 'px-4 py-2 bg-gradient-to-r from-pink-600 to-red-500 text-white rounded-full text-sm font-medium',
    isComingSoon: true
  },
  {
    id: 'ultra',
    name: 'Ultra Model',
    badgeClass: 'px-4 py-2 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-full text-sm font-medium',
    isComingSoon: true
  }
] as const;

// Audio models
const AUDIO_MODELS = [
  {
    id: 'basic-voice',
    name: 'Basic Voice',
    badgeClass: 'px-4 py-2 bg-black/60 backdrop-blur-sm text-white rounded-full text-sm font-medium',
    isComingSoon: false
  },
  {
    id: 'pro-voice',
    name: 'Pro Voice',
    badgeClass: 'px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-500 text-white rounded-full text-sm font-medium',
    isComingSoon: false
  },
  {
    id: 'music',
    name: 'Music Generator',
    badgeClass: 'px-4 py-2 bg-gradient-to-r from-emerald-600 to-teal-500 text-white rounded-full text-sm font-medium',
    isComingSoon: true
  }
] as const;

// Image generation showcase data
const IMAGE_GENERATION_IMAGES = [
  { src: "/images/basic-model-1.jpg", alt: "Basic model image 1", model: 'basic' },
  { src: "/images/pro-model-1.jpg", alt: "Pro model image 1", model: 'pro' },
  { src: "/images/basic-model-2.jpg", alt: "Basic model image 2", model: 'basic' },
  { src: "/images/ultra-model-1.jpg", alt: "Ultra model image 1", model: 'ultra' }
] as const;

// Video generation showcase data
const VIDEO_GENERATION_VIDEOS = [
  { src: "src/Videos/aws-video.mp4", model: 'basic', description: "Standard quality, optimized for speed" },
  { src: "src/Videos/google-video.mp4", model: 'pro', description: "Cinema-quality with advanced motion & effects" }
] as const;

// Audio generation showcase data
const AUDIO_GENERATION_SAMPLES = [
  { 
    id: 'voice-basic',
    title: "Welcome Message",
    model: 'basic-voice',
    duration: "0:08",
    waveform: "M2 12h20M6 8h12M4 16h16"
  },
  { 
    id: 'voice-pro',
    title: "Brand Narration",
    model: 'pro-voice',
    duration: "0:12",
    waveform: "M2 12h20M3 8h18M5 16h14"
  },
  { 
    id: 'music-sample',
    title: "Background Track",
    model: 'music',
    duration: "0:30",
    waveform: "M2 12h20M1 8h22M3 16h18"
  }
] as const;

// Reusable image component
interface FeatureImageProps {
  src: string;
  alt: string;
  modelId: 'basic' | 'pro' | 'ultra';
}

const FeatureImage = ({ src, alt, modelId }: FeatureImageProps) => {
  const model = FEATURE_MODELS.find(m => m.id === modelId);
  if (!model) return null;

  const badgeClass = modelId === 'pro' && alt.includes('Style Control') 
    ? model.styleControlBadgeClass 
    : model.badgeClass;

  return (
    <div className="group/image">
      <div className="relative overflow-hidden rounded-2xl shadow-xl">
        <img
          src={src}
          alt={alt}
          loading="lazy"
          className="w-full h-72 object-cover transition-transform duration-500 group-hover/image:scale-105"
        />
        <div className="absolute top-6 left-6 flex gap-3">
          <span className={badgeClass}>
            {model.name}
          </span>
          {model.isComingSoon && (
            <span className="px-3 py-1 bg-amber-500/20 backdrop-blur-sm text-amber-300 rounded-full text-sm font-medium">
              Coming Soon
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

// Audio card component
interface AudioCardProps {
  id: string;
  title: string;
  modelId: 'basic-voice' | 'pro-voice' | 'music';
  duration: string;
  waveform: string;
}

const AudioCard = ({ id, title, modelId, duration, waveform }: AudioCardProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const model = AUDIO_MODELS.find(m => m.id === modelId);
  if (!model) return null;

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
    setTimeout(() => setIsPlaying(false), 3000);
  };

  return (
    <div className="group">
      <div className="relative overflow-hidden rounded-2xl shadow-xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-gray-700/30">
        <div className="p-6 h-72 flex flex-col justify-center items-center">
          {/* Waveform */}
          <div className="w-full h-12 mb-4 flex items-center justify-center">
            <svg className="w-full h-full opacity-60" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              {waveform.split('M').slice(1).map((path, index) => (
                <path key={index} d={`M${path}`} strokeWidth="2" strokeLinecap="round" className={isPlaying ? "animate-pulse" : ""} />
              ))}
            </svg>
          </div>

          {/* Play Button */}
          <Button
            onClick={handlePlayPause}
            size="lg"
            className="w-12 h-12 rounded-full bg-white/10 hover:bg-white/20 border border-white/20 backdrop-blur-sm mb-4"
            disabled={model.isComingSoon}
          >
            {isPlaying ? <Pause className="w-4 h-4 text-white" /> : <Play className="w-4 h-4 text-white ml-0.5" />}
          </Button>

          <h4 className="text-white font-medium text-center mb-2">{title}</h4>
          <p className="text-white/60 text-sm">{duration}</p>
        </div>

        {/* Model Badge */}
        <div className="absolute top-6 left-6 flex gap-3">
          <span className={model.badgeClass}>{model.name}</span>
          {model.isComingSoon && (
            <span className="px-3 py-1 bg-amber-500/20 backdrop-blur-sm text-amber-300 rounded-full text-sm font-medium">
              Coming Soon
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

const FeaturesSectionWithAudio = () => {
  const navigate = useNavigate();

  return (
    <section className="relative overflow-hidden py-20 md:py-32">
      <div className="container relative z-20 mx-auto px-4 md:px-8">
        {/* Section header */}
        <div className="text-center mb-20">
          <h2 className="text-3xl md:text-5xl font-semibold mb-6 leading-tight tracking-tight text-white">
            Create with <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">AI-powered</span> precision
          </h2>
          <p className="text-lg text-white/80 mb-0 max-w-2xl mx-auto leading-relaxed">
            Generate stunning visuals, videos, and audio content with our advanced AI models designed for marketing excellence.
          </p>
        </div>

        {/* Features grid - 3 columns */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-16 lg:gap-12 max-w-7xl mx-auto">
          
          {/* Feature 1 - AI Image Generation */}
          <div className="group" data-section="image-generation">
            <div className="flex items-center gap-3 mb-8">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center">
                <Wand2 className="w-5 h-5 text-purple-400" />
              </div>
              <h3 className="text-xl font-medium bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent">AI Image Generation</h3>
            </div>

            <div className="space-y-6">
              {IMAGE_GENERATION_IMAGES.slice(0, 2).map((image, index) => (
                <FeatureImage key={index} src={image.src} alt={image.alt} modelId={image.model} />
              ))}
            </div>

            <p className="text-white/70 text-base leading-relaxed mt-6">
              Create stunning visuals with our <span className="text-purple-300 font-medium">Pro model</span> for enhanced quality and detail.
            </p>
          </div>

          {/* Feature 2 - Video Generation */}
          <div className="group" data-section="video-generation">
            <div className="flex items-center gap-3 mb-8">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-violet-500/20 to-purple-500/20 flex items-center justify-center">
                <Video className="w-5 h-5 text-violet-400" />
              </div>
              <h3 className="text-xl font-medium bg-gradient-to-r from-violet-300 to-purple-300 bg-clip-text text-transparent">Video Generation</h3>
            </div>

            <div className="space-y-6">
              {VIDEO_GENERATION_VIDEOS.map((video, index) => (
                <div key={index} className="group/video">
                  <div className="relative overflow-hidden rounded-2xl shadow-xl">
                    <video className="w-full h-72 object-cover" controls preload="metadata">
                      <source src={video.src} type="video/mp4" />
                    </video>
                    <div className="absolute top-6 left-6 flex gap-3">
                      <span className={FEATURE_MODELS.find(m => m.id === video.model)?.badgeClass}>
                        {FEATURE_MODELS.find(m => m.id === video.model)?.name}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <p className="text-white/70 text-base leading-relaxed mt-6">
              Generate cinematic videos with <span className="text-violet-300 font-medium">Pro quality</span> motion and effects.
            </p>
          </div>

          {/* Feature 3 - Audio Generation */}
          <div className="group" data-section="audio-generation">
            <div className="flex items-center gap-3 mb-8">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500/20 to-teal-500/20 flex items-center justify-center">
                <Volume2 className="w-5 h-5 text-emerald-400" />
              </div>
              <h3 className="text-xl font-medium bg-gradient-to-r from-emerald-300 to-teal-300 bg-clip-text text-transparent">Audio Generation</h3>
            </div>

            <div className="space-y-6">
              {AUDIO_GENERATION_SAMPLES.map((audio) => (
                <AudioCard
                  key={audio.id}
                  id={audio.id}
                  title={audio.title}
                  modelId={audio.model}
                  duration={audio.duration}
                  waveform={audio.waveform}
                />
              ))}
            </div>

            <p className="text-white/70 text-base leading-relaxed mt-6">
              Create professional voice content with <span className="text-emerald-300 font-medium">Pro Voice</span> enhanced clarity.
            </p>
          </div>
        </div>

        {/* CTA button */}
        <div className="text-center mt-16">
          <Button
            size="lg"
            onClick={() => navigate("/signup")}
            className="bg-gradient-to-r from-purple-600 to-pink-500 text-white hover:opacity-90 h-12 px-8 rounded-full font-medium"
          >
            Start Creating
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSectionWithAudio;
